import type { NextApiRequest, NextApiResponse } from 'next';
import { getStripeInstance, type StripePaymentIntentRequest } from '@/lib/stripe';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const {
      customerId,
      amount,
      currency,
      receipt_email,
      capture_method,
      isUS
    }: StripePaymentIntentRequest = req.body;

    if (!customerId || !amount || !currency) {
      return res.status(400).json({ 
        error: 'Customer ID, amount, and currency are required' 
      });
    }

    const stripeService = getStripeInstance(isUS === 'true');

    // Creates a temporary secret key linked with the customer 
    const ephemeralKey = await stripeService.ephemeralKeys.create(
      { customer: customerId },
      { apiVersion: '2020-08-27' }
    );

    // Creates a new payment intent with amount passed in from the client
    const paymentIntent = await stripeService.paymentIntents.create({
      amount: parseInt(amount),
      currency,
      customer: customerId,
      receipt_email,
      capture_method: capture_method as 'automatic' | 'manual' | undefined,
      // Enable all available payment methods
      payment_method_types: [
        'card',
        'apple_pay',
        'google_pay',
        'link',
        'paypal',
        'klarna',
        'afterpay_clearpay',
        'affirm',
        'cashapp',
        'us_bank_account'
      ],
      automatic_payment_methods: {
        enabled: true,
        allow_redirects: 'always'
      },
      setup_future_usage: 'off_session'
    });

    res.status(200).json({
      isUSStripeUsed: Boolean(isUS === 'true'),
      paymentIntent,
      ephemeralKey: ephemeralKey.secret,
      success: true,
    });

  } catch (error) {
    console.error('Error creating payment intent:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({
      success: false,
      error: errorMessage
    });
  }
}
