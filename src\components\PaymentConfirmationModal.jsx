'use client'

import React from 'react'
import { Check, ArrowLeft, User } from 'lucide-react'

const PaymentConfirmationModal = ({ 
  isOpen, 
  onClose, 
  paymentDetails = {},
  onContinue = null,
  showBackButton = true
}) => {
  if (!isOpen) return null

  const {
    serviceName = "Name posuere consectetur non",
    serviceProvider = "<PERSON><PERSON>yi <PERSON>",
    estimatedTime = "3 days 5 h",
    amount = "150",
    currency = "$",
    paymentMethod = "Apple Pay"
  } = paymentDetails

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-sm w-full mx-auto shadow-2xl">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          {showBackButton && (
            <button 
              onClick={onClose}
              className="flex items-center text-gray-600 hover:text-gray-800"
            >
              <ArrowLeft className="w-5 h-5 mr-1" />
              Back
            </button>
          )}
          <h2 className="text-lg font-semibold text-gray-900">
            Basket
          </h2>
          <div className="w-12"></div> {/* Spacer for centering */}
        </div>

        {/* Service Provider Info */}
        <div className="p-4 border-b border-gray-100">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
              <User className="w-6 h-6 text-gray-600" />
            </div>
            <div>
              <div className="font-medium text-gray-900">{serviceProvider}</div>
            </div>
          </div>
        </div>

        {/* Service Details */}
        <div className="p-4 border-b border-gray-100">
          <div className="text-sm font-medium text-gray-900 mb-2">
            Service: <span className="font-normal">{serviceName}</span>
          </div>
          <div className="text-sm text-gray-600 mb-2">
            Approximate time: <span className="font-medium">{estimatedTime}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Total</span>
            <span className="text-lg font-bold text-gray-900">
              {currency}{amount}
            </span>
          </div>
        </div>

        {/* Payment Confirmation */}
        <div className="p-6 text-center">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Check className="w-8 h-8 text-green-600" />
          </div>
          
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Thank you
          </h3>
          
          <p className="text-sm text-gray-600 mb-6 leading-relaxed">
            The amount has been <strong>pre-authorized</strong>. Your order will be{' '}
            <strong>confirmed by the service provider within 48 hours</strong>.
          </p>

          <button
            onClick={onContinue || onClose}
            className="w-full bg-white border border-gray-300 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-50 transition-colors"
          >
            OK
          </button>
        </div>
      </div>
    </div>
  )
}

export default PaymentConfirmationModal
