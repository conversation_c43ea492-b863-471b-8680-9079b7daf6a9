'use client'

import React, { useState } from 'react'
import PaymentMethodSelector from '../../components/PaymentMethodSelector'
import PaymentConfirmationModal from '../../components/PaymentConfirmationModal'

export default function PaymentMethodsPage() {
  const [selectedMethod, setSelectedMethod] = useState(null)
  const [showConfirmation, setShowConfirmation] = useState(false)

  const handlePaymentMethodSelect = (method) => {
    setSelectedMethod(method)
    
    // Simulate payment processing
    if (!method.isAddCard) {
      setTimeout(() => {
        setShowConfirmation(true)
      }, 1000)
    }
  }

  const handleConfirmationClose = () => {
    setShowConfirmation(false)
    setSelectedMethod(null)
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Mobile-first design */}
      <div className="max-w-sm mx-auto bg-white min-h-screen">
        <PaymentMethodSelector
          onPaymentMethodSelect={handlePaymentMethodSelect}
          selectedMethod={selectedMethod}
          showHeader={true}
        />
        
        {selectedMethod && !selectedMethod.isAddCard && (
          <div className="p-4 border-t border-gray-200">
            <button
              onClick={() => setShowConfirmation(true)}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-medium transition-colors"
            >
              Continue with {selectedMethod.displayName}
            </button>
          </div>
        )}
      </div>

      <PaymentConfirmationModal
        isOpen={showConfirmation}
        onClose={handleConfirmationClose}
        paymentDetails={{
          serviceName: "Name posuere consectetur non",
          serviceProvider: "Ziyi Zhang",
          estimatedTime: "3 days 5 h",
          amount: "150",
          currency: "$",
          paymentMethod: selectedMethod?.displayName || "Card"
        }}
        onContinue={() => {
          handleConfirmationClose()
          // Navigate to success page or next step
          console.log('Payment confirmed!')
        }}
      />
    </div>
  )
}
