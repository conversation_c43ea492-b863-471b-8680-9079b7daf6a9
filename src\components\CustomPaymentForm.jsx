'use client'

import { useState, useEffect } from 'react'
import {
  Elements,
  PaymentElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js'
import { loadStripe } from '@stripe/stripe-js'

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY)

// Custom styling for Stripe Elements
const elementOptions = {
  style: {
    base: {
      fontSize: '16px',
      color: '#424770',
      '::placeholder': {
        color: '#aab7c4',
      },
      padding: '12px',
    },
    invalid: {
      color: '#9e2146',
    },
  },
}

function PaymentForm({ 
  amount = 6500,
  currency = 'usd',
  productName = 'Pure set',
  productImage = '/api/placeholder/300/300',
  onSuccess,
  onError 
}) {
  const stripe = useStripe()
  const elements = useElements()
  const [loading, setLoading] = useState(false)
  const [email, setEmail] = useState('')
  const [clientSecret, setClientSecret] = useState('')

  // Create payment intent when component mounts
  useEffect(() => {
    const createPaymentIntent = async () => {
      try {
        const response = await fetch('/api/stripe/payment-intent/create-custom', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            amount,
            currency,
            metadata: {
              productName,
            }
          }),
        })

        const data = await response.json()
        if (data.clientSecret) {
          setClientSecret(data.clientSecret)
        }
      } catch (error) {
        console.error('Error creating payment intent:', error)
        if (onError) onError(error)
      }
    }

    createPaymentIntent()
  }, [amount, currency, productName, onError])

  const handleSubmit = async (event) => {
    event.preventDefault()

    if (!stripe || !elements || !clientSecret) {
      return
    }

    setLoading(true)

    // Use the more flexible confirmPayment method that works with all payment methods
    const { error, paymentIntent } = await stripe.confirmPayment({
      elements,
      confirmParams: {
        return_url: `${window.location.origin}/payment-success`,
        receipt_email: email,
      },
      redirect: 'if_required'
    })

    if (error) {
      console.error('Payment failed:', error)
      if (onError) onError(error)
    } else {
      console.log('Payment succeeded:', paymentIntent)
      if (onSuccess) onSuccess(paymentIntent)
    }

    setLoading(false)
  }

  const formatPrice = (amount, currency) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount / 100)
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div className="grid grid-cols-1 lg:grid-cols-2">
            {/* Left side - Product details */}
            <div className="p-8 bg-gray-50">
              <div className="mb-6">
                <h2 className="text-2xl font-semibold text-gray-900 mb-2">{productName}</h2>
                <p className="text-3xl font-bold text-gray-900">{formatPrice(amount, currency)}</p>
              </div>
              
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <img 
                  src={productImage} 
                  alt={productName}
                  className="w-full h-64 object-cover rounded-lg"
                />
              </div>
            </div>

            {/* Right side - Payment form */}
            <div className="p-8">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">Choose Payment Method</h3>
              
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Email */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email
                  </label>
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    className="w-full px-3 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                </div>

                {/* Payment method section */}
                <div>
                  <h4 className="text-lg font-medium text-gray-900 mb-4">Payment method</h4>

                  {/* Payment Element - supports all payment methods */}
                  <div className="space-y-4">
                    <div>
                      <PaymentElement
                        options={{
                          layout: 'tabs',
                          wallets: {
                            applePay: 'auto',
                            googlePay: 'auto'
                          },
                          fields: {
                            billingDetails: 'auto'
                          }
                        }}
                      />
                    </div>

                    {/* PaymentElement handles all billing details automatically */}
                  </div>
                </div>

                {/* Pay button */}
                <button
                  type="submit"
                  disabled={!stripe || loading}
                  className="w-full bg-blue-900 hover:bg-blue-800 disabled:bg-gray-400 text-white font-medium py-4 px-6 rounded-md transition-colors text-lg"
                >
                  {loading ? 'Processing...' : 'Pay'}
                </button>

                {/* Powered by Stripe */}
                <div className="text-center text-sm text-gray-500">
                  Powered by <span className="font-medium">stripe</span> | Terms | Privacy
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function CustomPaymentForm(props) {
  return (
    <Elements stripe={stripePromise}>
      <PaymentForm {...props} />
    </Elements>
  )
}
