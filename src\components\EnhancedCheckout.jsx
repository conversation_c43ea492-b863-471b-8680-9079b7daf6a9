'use client'

import React, { useState, useEffect } from 'react'
import { loadStripe } from '@stripe/stripe-js'
import { Elements, useStripe, useElements, PaymentElement } from '@stripe/react-stripe-js'
import PaymentMethodSelector from './PaymentMethodSelector'
import PaymentConfirmationModal from './PaymentConfirmationModal'

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY)

const CheckoutForm = ({ 
  amount, 
  currency, 
  productName, 
  onSuccess, 
  onError,
  clientSecret 
}) => {
  const stripe = useStripe()
  const elements = useElements()
  const [loading, setLoading] = useState(false)
  const [showPaymentMethods, setShowPaymentMethods] = useState(true)
  const [showConfirmation, setShowConfirmation] = useState(false)
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(null)
  const [paymentDetails, setPaymentDetails] = useState({})

  const handlePaymentMethodSelect = (method) => {
    setSelectedPaymentMethod(method)
    if (method.isAddCard) {
      // Show Stripe's payment element for adding new card
      setShowPaymentMethods(false)
    } else {
      // For existing methods, proceed directly to confirmation
      handlePayment(method)
    }
  }

  const handlePayment = async (paymentMethod = selectedPaymentMethod) => {
    if (!stripe || !elements || !clientSecret) {
      return
    }

    setLoading(true)

    try {
      let result

      if (paymentMethod?.type === 'wallet') {
        // Handle wallet payments (Apple Pay, Google Pay)
        result = await stripe.confirmPayment({
          elements,
          confirmParams: {
            return_url: `${window.location.origin}/payment-success`,
          },
          redirect: 'if_required'
        })
      } else {
        // Handle card payments
        result = await stripe.confirmPayment({
          elements,
          confirmParams: {
            return_url: `${window.location.origin}/payment-success`,
          },
          redirect: 'if_required'
        })
      }

      if (result.error) {
        console.error('Payment failed:', result.error)
        if (onError) onError(result.error)
      } else {
        console.log('Payment succeeded:', result.paymentIntent)
        setPaymentDetails({
          serviceName: productName,
          amount: (amount / 100).toString(),
          currency: currency === 'usd' ? '$' : currency,
          paymentMethod: paymentMethod?.displayName || 'Card'
        })
        setShowConfirmation(true)
        if (onSuccess) onSuccess(result.paymentIntent)
      }
    } catch (error) {
      console.error('Payment error:', error)
      if (onError) onError(error)
    }

    setLoading(false)
  }

  const handleSubmit = async (event) => {
    event.preventDefault()
    await handlePayment()
  }

  if (showConfirmation) {
    return (
      <PaymentConfirmationModal
        isOpen={true}
        paymentDetails={paymentDetails}
        onClose={() => setShowConfirmation(false)}
        onContinue={() => {
          setShowConfirmation(false)
          // Redirect to success page or handle completion
          window.location.href = '/payment-success'
        }}
      />
    )
  }

  if (showPaymentMethods) {
    return (
      <div className="max-w-md mx-auto">
        <PaymentMethodSelector
          onPaymentMethodSelect={handlePaymentMethodSelect}
          selectedMethod={selectedPaymentMethod}
        />
      </div>
    )
  }

  return (
    <div className="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6">
      <div className="mb-6">
        <button
          onClick={() => setShowPaymentMethods(true)}
          className="text-blue-600 hover:text-blue-800 text-sm font-medium"
        >
          ← Back to Payment Methods
        </button>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Add New Payment Method
          </h3>
          <PaymentElement />
        </div>

        <button
          type="submit"
          disabled={!stripe || loading}
          className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
            loading
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700 text-white'
          }`}
        >
          {loading ? 'Processing...' : `Pay ${currency === 'usd' ? '$' : currency}${(amount / 100).toFixed(2)}`}
        </button>
      </form>
    </div>
  )
}

const EnhancedCheckout = ({
  amount = 2000,
  currency = 'usd',
  productName = 'Sample Product',
  productDescription = '',
  onSuccess,
  onError
}) => {
  const [clientSecret, setClientSecret] = useState('')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    // Create payment intent
    fetch('/api/stripe/payment-intent/create-custom', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        amount,
        currency,
        metadata: {
          productName,
          productDescription
        }
      }),
    })
      .then((res) => res.json())
      .then((data) => {
        if (data.error) {
          setError(data.error)
        } else {
          setClientSecret(data.clientSecret)
        }
        setLoading(false)
      })
      .catch((err) => {
        setError('Failed to initialize payment')
        setLoading(false)
      })
  }, [amount, currency, productName, productDescription])

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-red-600 mb-2">Payment Error</h2>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    )
  }

  const options = {
    clientSecret,
    appearance: {
      theme: 'stripe',
      variables: {
        colorPrimary: '#2563eb',
      },
    },
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-lg mx-auto px-4">
        <Elements options={options} stripe={stripePromise}>
          <CheckoutForm
            amount={amount}
            currency={currency}
            productName={productName}
            onSuccess={onSuccess}
            onError={onError}
            clientSecret={clientSecret}
          />
        </Elements>
      </div>
    </div>
  )
}

export default EnhancedCheckout
