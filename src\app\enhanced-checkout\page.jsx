'use client'

import React from 'react'
import EnhancedCheckout from '../../components/EnhancedCheckout'

export default function EnhancedCheckoutPage() {
  const handleSuccess = (paymentIntent) => {
    console.log('Payment successful:', paymentIntent)
    // Handle successful payment
  }

  const handleError = (error) => {
    console.error('Payment error:', error)
    // Handle payment error
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Enhanced Payment Experience
          </h1>
          <p className="text-gray-600">
            Choose from multiple payment methods including Apple Pay, Google Pay, and cards
          </p>
        </div>

        <EnhancedCheckout
          amount={15000} // $150.00
          currency="usd"
          productName="Premium Service Package"
          productDescription="Complete service package with 48-hour confirmation"
          onSuccess={handleSuccess}
          onError={handleError}
        />
      </div>
    </div>
  )
}
