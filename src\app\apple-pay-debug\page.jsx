'use client'

import React from 'react'
import ApplePayDebugger from '../../components/ApplePayDebugger'

export default function ApplePayDebugPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Apple Pay Debug Tool
          </h1>
          <p className="text-gray-600">
            Check why Apple Pay might not be working in your environment
          </p>
        </div>

        <ApplePayDebugger />

        <div className="mt-8 bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Common Issues & Solutions</h2>
          
          <div className="space-y-6">
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">🔒 HTTPS Required</h3>
              <p className="text-gray-600 mb-2">
                Apple Pay only works on HTTPS. If you're on localhost, use ngrok:
              </p>
              <div className="bg-gray-100 p-3 rounded-lg font-mono text-sm">
                <div>npm install -g ngrok</div>
                <div>ngrok http 3000</div>
              </div>
            </div>

            <div>
              <h3 className="font-semibold text-gray-900 mb-2">🌐 Browser Support</h3>
              <p className="text-gray-600">
                Apple Pay works best on Safari on iOS and macOS. Other browsers have limited support.
              </p>
            </div>

            <div>
              <h3 className="font-semibold text-gray-900 mb-2">📱 Device Requirements</h3>
              <p className="text-gray-600">
                You need an iOS device with Touch ID/Face ID or a Mac with Touch ID, and Apple Pay must be set up.
              </p>
            </div>

            <div>
              <h3 className="font-semibold text-gray-900 mb-2">⚙️ Stripe Configuration</h3>
              <p className="text-gray-600 mb-2">
                Make sure your Stripe account has Apple Pay enabled and your domain is verified.
              </p>
              <ul className="list-disc list-inside text-gray-600 space-y-1">
                <li>Go to Stripe Dashboard → Settings → Payment Methods</li>
                <li>Enable Apple Pay</li>
                <li>Add and verify your domain</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="mt-8 text-center">
          <div className="space-x-4">
            <a
              href="/apple-pay-test"
              className="inline-block bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Test Apple Pay
            </a>
            <a
              href="/payment-methods"
              className="inline-block bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Payment Methods UI
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
