'use client'

import React, { useState, useEffect } from 'react'
import { loadStripe } from '@stripe/stripe-js'
import { Elements, PaymentElement, useStripe, useElements } from '@stripe/react-stripe-js'

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY)

const ApplePayForm = () => {
  const stripe = useStripe()
  const elements = useElements()
  const [loading, setLoading] = useState(false)
  const [clientSecret, setClientSecret] = useState('')
  const [canMakePayment, setCanMakePayment] = useState(false)
  const [error, setError] = useState(null)

  useEffect(() => {
    // Check if Apple Pay is available
    const checkApplePayAvailability = async () => {
      if (window.ApplePaySession && ApplePaySession.canMakePayments()) {
        setCanMakePayment(true)
      }
    }

    checkApplePayAvailability()

    // Create payment intent
    const createPaymentIntent = async () => {
      try {
        const response = await fetch('/api/stripe/payment-intent/create-custom', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            amount: 2000, // $20.00
            currency: 'usd',
            metadata: {
              productName: 'Apple Pay Test Product'
            }
          }),
        })

        const data = await response.json()
        if (data.clientSecret) {
          setClientSecret(data.clientSecret)
        } else {
          setError('Failed to create payment intent')
        }
      } catch (err) {
        setError('Failed to initialize payment')
      }
    }

    createPaymentIntent()
  }, [])

  const handleSubmit = async (event) => {
    event.preventDefault()

    if (!stripe || !elements || !clientSecret) {
      return
    }

    setLoading(true)
    setError(null)

    try {
      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/payment-success`,
        },
        redirect: 'if_required'
      })

      if (error) {
        setError(error.message)
        console.error('Payment failed:', error)
      } else {
        console.log('Payment succeeded:', paymentIntent)
        // Handle success
        alert('Payment successful!')
      }
    } catch (err) {
      setError('Payment failed')
      console.error('Payment error:', err)
    }

    setLoading(false)
  }

  if (!clientSecret) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Initializing payment...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">Apple Pay Test</h2>
      
      {!canMakePayment && (
        <div className="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800 text-sm">
            <strong>Note:</strong> Apple Pay is not available on this device/browser. 
            You need to use Safari on iOS/macOS with Apple Pay set up.
          </p>
        </div>
      )}

      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-800 text-sm">{error}</p>
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div className="mb-6">
          <PaymentElement 
            options={{
              layout: 'tabs',
              wallets: {
                applePay: 'auto',
                googlePay: 'auto'
              },
              fields: {
                billingDetails: 'auto'
              }
            }}
          />
        </div>

        <button
          type="submit"
          disabled={!stripe || loading}
          className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
            loading
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700 text-white'
          }`}
        >
          {loading ? 'Processing...' : 'Pay $20.00'}
        </button>
      </form>

      <div className="mt-6 text-sm text-gray-600">
        <h3 className="font-semibold mb-2">Apple Pay Requirements:</h3>
        <ul className="list-disc list-inside space-y-1">
          <li>Must be on HTTPS (not localhost)</li>
          <li>Must use Safari on iOS/macOS</li>
          <li>Apple Pay must be set up on device</li>
          <li>Domain must be verified with Apple</li>
        </ul>
      </div>
    </div>
  )
}

export default function ApplePayTestPage() {
  const options = {
    appearance: {
      theme: 'stripe',
      variables: {
        colorPrimary: '#2563eb',
      },
    },
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-lg mx-auto px-4">
        <Elements options={options} stripe={stripePromise}>
          <ApplePayForm />
        </Elements>
      </div>
    </div>
  )
}
