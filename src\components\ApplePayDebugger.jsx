'use client'

import React, { useState, useEffect } from 'react'
import { CheckCircle, XCircle, AlertCircle } from 'lucide-react'

const ApplePayDebugger = () => {
  const [checks, setChecks] = useState({
    https: false,
    applePayAPI: false,
    canMakePayments: false,
    safari: false,
    device: 'unknown'
  })

  useEffect(() => {
    const runChecks = () => {
      const newChecks = {
        // Check if HTTPS
        https: window.location.protocol === 'https:',
        
        // Check if Apple Pay API is available
        applePayAPI: typeof window.ApplePaySession !== 'undefined',
        
        // Check if can make payments
        canMakePayments: false,
        
        // Check if Safari
        safari: /^((?!chrome|android).)*safari/i.test(navigator.userAgent),
        
        // Detect device
        device: /iPad|iPhone|iPod/.test(navigator.userAgent) ? 'iOS' : 
                /Macintosh/.test(navigator.userAgent) ? 'macOS' : 'Other'
      }

      // Check if can make payments (only if ApplePaySession exists)
      if (newChecks.applePayAPI && window.ApplePaySession) {
        try {
          newChecks.canMakePayments = ApplePaySession.canMakePayments()
        } catch (e) {
          newChecks.canMakePayments = false
        }
      }

      setChecks(newChecks)
    }

    runChecks()
  }, [])

  const CheckItem = ({ label, status, description }) => (
    <div className="flex items-start space-x-3 p-3 rounded-lg border">
      <div className="flex-shrink-0 mt-0.5">
        {status === 'pass' && <CheckCircle className="w-5 h-5 text-green-600" />}
        {status === 'fail' && <XCircle className="w-5 h-5 text-red-600" />}
        {status === 'warning' && <AlertCircle className="w-5 h-5 text-yellow-600" />}
      </div>
      <div className="flex-1">
        <div className="font-medium text-gray-900">{label}</div>
        <div className="text-sm text-gray-600">{description}</div>
      </div>
    </div>
  )

  const getOverallStatus = () => {
    if (checks.https && checks.applePayAPI && checks.canMakePayments && checks.safari) {
      return 'ready'
    } else if (!checks.https) {
      return 'needs-https'
    } else if (!checks.safari || checks.device === 'Other') {
      return 'wrong-browser'
    } else {
      return 'not-ready'
    }
  }

  const status = getOverallStatus()

  return (
    <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-6">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">Apple Pay Compatibility Check</h2>
      
      {/* Overall Status */}
      <div className={`p-4 rounded-lg mb-6 ${
        status === 'ready' ? 'bg-green-50 border border-green-200' :
        status === 'needs-https' ? 'bg-red-50 border border-red-200' :
        status === 'wrong-browser' ? 'bg-yellow-50 border border-yellow-200' :
        'bg-red-50 border border-red-200'
      }`}>
        <div className="flex items-center space-x-2">
          {status === 'ready' && <CheckCircle className="w-6 h-6 text-green-600" />}
          {status !== 'ready' && <XCircle className="w-6 h-6 text-red-600" />}
          <h3 className="text-lg font-semibold">
            {status === 'ready' && 'Apple Pay Ready! 🎉'}
            {status === 'needs-https' && 'HTTPS Required'}
            {status === 'wrong-browser' && 'Wrong Browser/Device'}
            {status === 'not-ready' && 'Apple Pay Not Available'}
          </h3>
        </div>
        <p className="mt-2 text-sm">
          {status === 'ready' && 'All requirements met. Apple Pay should work properly.'}
          {status === 'needs-https' && 'Apple Pay requires HTTPS. Use ngrok or deploy to a secure domain.'}
          {status === 'wrong-browser' && 'Apple Pay works best on Safari on iOS/macOS devices.'}
          {status === 'not-ready' && 'Some requirements are not met. Check the details below.'}
        </p>
      </div>

      {/* Detailed Checks */}
      <div className="space-y-4">
        <CheckItem
          label="HTTPS Protocol"
          status={checks.https ? 'pass' : 'fail'}
          description={checks.https ? 
            'Using secure HTTPS connection ✓' : 
            'Apple Pay requires HTTPS. Currently using HTTP.'
          }
        />

        <CheckItem
          label="Apple Pay API"
          status={checks.applePayAPI ? 'pass' : 'fail'}
          description={checks.applePayAPI ? 
            'Apple Pay JavaScript API is available ✓' : 
            'Apple Pay API not found. Use Safari on iOS/macOS.'
          }
        />

        <CheckItem
          label="Can Make Payments"
          status={checks.canMakePayments ? 'pass' : 'fail'}
          description={checks.canMakePayments ? 
            'Device can make Apple Pay payments ✓' : 
            'Apple Pay not set up on this device or not supported.'
          }
        />

        <CheckItem
          label="Browser Compatibility"
          status={checks.safari ? 'pass' : 'warning'}
          description={checks.safari ? 
            'Using Safari browser ✓' : 
            'For best Apple Pay support, use Safari on iOS/macOS.'
          }
        />

        <CheckItem
          label="Device Type"
          status={checks.device === 'iOS' || checks.device === 'macOS' ? 'pass' : 'warning'}
          description={`Detected: ${checks.device}. Apple Pay works best on iOS and macOS devices.`}
        />
      </div>

      {/* Solutions */}
      <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h3 className="font-semibold text-blue-900 mb-2">Quick Solutions:</h3>
        <ul className="text-sm text-blue-800 space-y-1">
          {!checks.https && (
            <li>• Use ngrok: <code className="bg-blue-100 px-1 rounded">ngrok http 3000</code></li>
          )}
          {!checks.safari && (
            <li>• Switch to Safari browser on iOS or macOS</li>
          )}
          {!checks.canMakePayments && (
            <li>• Set up Apple Pay in your device's Wallet app</li>
          )}
          <li>• Deploy to production with HTTPS for full testing</li>
        </ul>
      </div>

      {/* Current Environment Info */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-semibold text-gray-900 mb-2">Environment Info:</h3>
        <div className="text-sm text-gray-600 space-y-1">
          <div>Protocol: <code>{window.location.protocol}</code></div>
          <div>Host: <code>{window.location.host}</code></div>
          <div>User Agent: <code className="text-xs">{navigator.userAgent}</code></div>
        </div>
      </div>
    </div>
  )
}

export default ApplePayDebugger
