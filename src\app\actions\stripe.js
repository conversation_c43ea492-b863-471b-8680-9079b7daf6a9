import 'server-only'

import Stripe from 'stripe'

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY)

export async function fetchClientSecret(options = {}) {
  const {
    amount = 2000,
    currency = 'usd',
    productName = 'Sample Product',
    productDescription = '',
    customerId = null,
    metadata = {}
  } = options

  // Create a checkout session for embedded checkout with all payment methods enabled
  const session = await stripe.checkout.sessions.create({
    ui_mode: 'embedded',
    line_items: [
      {
        price_data: {
          currency,
          product_data: {
            name: productName,
            description: productDescription,
          },
          unit_amount: amount,
        },
        quantity: 1,
      },
    ],
    mode: 'payment',
    customer: customerId,
    metadata,
    // Enable all available payment methods
    payment_method_types: [
      'card',
      'apple_pay',
      'google_pay',
      'link',
      'paypal',
      'klarna',
      'afterpay_clearpay',
      'affirm',
      'cashapp',
      'us_bank_account'
    ],
    // Automatic payment methods configuration
    automatic_payment_methods: {
      enabled: true,
      allow_redirects: 'always'
    },
    return_url: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/checkout/return?session_id={CHECKOUT_SESSION_ID}`,
  })

  return session.client_secret
}