# Apple Pay Setup Guide

## Why Apple Pay Redirects to Card Payment

Apple Pay has strict requirements that must be met for it to work properly. If these aren't met, <PERSON><PERSON> will fall back to regular card payment.

## Requirements for Apple Pay

### 1. **HTTPS Required**
- Apple Pay **ONLY** works on HTTPS domains
- It will **NOT** work on `localhost` or `http://` URLs
- You need to deploy to a secure domain or use a tunneling service

### 2. **Domain Verification**
- Your domain must be verified with Apple
- This is done through your Stripe Dashboard

### 3. **Supported Browsers**
- **Safari** on iOS/macOS (primary support)
- **Chrome** on iOS (limited support)
- **NOT supported** on Chrome/Firefox on desktop

### 4. **Device Requirements**
- iOS device with Touch ID, Face ID, or Apple Watch
- macOS device with Touch ID or Apple Watch
- Apple Pay must be set up on the device

## How to Fix the Issue

### Option 1: Use a Tunneling Service (Recommended for Development)

1. **Install ngrok:**
   ```bash
   npm install -g ngrok
   ```

2. **Start your Next.js app:**
   ```bash
   npm run dev
   ```

3. **In another terminal, create HTTPS tunnel:**
   ```bash
   ngrok http 3000
   ```

4. **Use the HTTPS URL provided by ngrok** (e.g., `https://abc123.ngrok.io`)

### Option 2: Deploy to Production

Deploy your app to a production environment with HTTPS:
- Vercel
- Netlify  
- AWS
- Any hosting service with SSL

### Option 3: Test with Stripe's Test Mode

1. **Go to Stripe Dashboard** → Settings → Payment Methods
2. **Enable Apple Pay** for your account
3. **Add your domain** for Apple Pay verification
4. **Use the test environment** on a real iOS device

## Updated Code Changes Made

### 1. **Removed Conflicting Configuration**
```javascript
// OLD - Conflicting configuration
payment_method_types: ['card', 'apple_pay', 'google_pay', ...],
automatic_payment_methods: { enabled: true }

// NEW - Let Stripe handle it automatically
automatic_payment_methods: { 
  enabled: true,
  allow_redirects: 'always'
}
```

### 2. **Updated PaymentElement Options**
```javascript
<PaymentElement 
  options={{
    layout: 'tabs',
    wallets: {
      applePay: 'auto',
      googlePay: 'auto'
    },
    fields: {
      billingDetails: 'auto'
    }
  }}
/>
```

## Testing Apple Pay

### Test Pages Created:
1. **`/apple-pay-test`** - Dedicated Apple Pay testing page
2. **`/payment-methods`** - Payment method selection UI
3. **`/enhanced-checkout`** - Full checkout experience

### How to Test:

1. **Deploy to HTTPS domain** or use ngrok
2. **Open on Safari on iOS/macOS**
3. **Ensure Apple Pay is set up** on your device
4. **Visit the test page**
5. **Apple Pay should appear** as an option

## Expected Behavior

### ✅ **When Apple Pay Works:**
- You'll see Apple Pay button in the payment options
- Clicking it opens the Apple Pay sheet
- You can authenticate with Touch ID/Face ID
- Payment processes through Apple Pay

### ❌ **When Apple Pay Doesn't Work:**
- Only card payment options appear
- Apple Pay button is hidden
- Falls back to regular card entry

## Quick Debug Checklist

- [ ] Using HTTPS (not localhost)
- [ ] Testing on Safari (iOS/macOS)
- [ ] Apple Pay set up on device
- [ ] Domain verified in Stripe Dashboard
- [ ] Using `automatic_payment_methods: { enabled: true }`
- [ ] Not specifying conflicting `payment_method_types`

## Production Setup

For production, you'll need to:

1. **Verify your domain** in Stripe Dashboard
2. **Enable Apple Pay** in your Stripe account settings
3. **Deploy to HTTPS** domain
4. **Test on real iOS devices**

The code is now properly configured - the issue is likely the development environment (localhost) not meeting Apple Pay's HTTPS requirement.
