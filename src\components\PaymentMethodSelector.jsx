'use client'

import React, { useState } from 'react'
import { Check, ChevronRight, CreditCard, Smartphone } from 'lucide-react'

const PaymentMethodSelector = ({ 
  onPaymentMethodSelect, 
  selectedMethod = null,
  showHeader = true,
  className = ""
}) => {
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(selectedMethod)

  const paymentMethods = [
    {
      id: 'visa_1881',
      type: 'card',
      brand: 'visa',
      name: 'Visa',
      last4: '1881',
      icon: '💳',
      displayName: 'Visa Ending in 1881'
    },
    {
      id: 'mastercard_8210',
      type: 'card',
      brand: 'mastercard',
      name: 'MasterCard',
      last4: '8210',
      icon: '💳',
      displayName: 'MasterCard Ending in 8210'
    },
    {
      id: 'amex_0005',
      type: 'card',
      brand: 'amex',
      name: 'American Express',
      last4: '0005',
      icon: '💳',
      displayName: 'American Express Ending in 0005'
    },
    {
      id: 'apple_pay',
      type: 'wallet',
      name: 'Apple Pay',
      icon: '🍎',
      displayName: 'Apple Pay',
      available: true
    },
    {
      id: 'google_pay',
      type: 'wallet',
      name: 'Google Pay',
      icon: '🟢',
      displayName: 'Google Pay',
      available: true
    },
    {
      id: 'add_new_card',
      type: 'add_card',
      name: 'Add New Card',
      icon: '➕',
      displayName: 'Add New Card...',
      isAddCard: true
    }
  ]

  const handleMethodSelect = (method) => {
    setSelectedPaymentMethod(method)
    if (onPaymentMethodSelect) {
      onPaymentMethodSelect(method)
    }
  }

  return (
    <div className={`bg-white ${className}`}>
      {showHeader && (
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <button className="text-blue-600 text-base font-medium">
            Cancel
          </button>
          <h2 className="text-lg font-semibold text-gray-900">
            Payment Method
          </h2>
          <button className="text-blue-600 text-base font-medium">
            Edit
          </button>
        </div>
      )}

      <div className="p-4">
        {/* Card Preview Section */}
        <div className="mb-6">
          <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl p-4 text-white shadow-lg">
            <div className="flex items-center justify-between mb-4">
              <CreditCard className="w-8 h-8" />
              <div className="text-right">
                <div className="text-xs opacity-75">VISA</div>
              </div>
            </div>
            <div className="text-lg font-mono tracking-wider mb-2">
              •••• •••• •••• 1881
            </div>
            <div className="text-sm opacity-90">
              Card Holder Name
            </div>
          </div>
        </div>

        {/* Payment Methods List */}
        <div className="space-y-0 border border-gray-200 rounded-lg overflow-hidden">
          {paymentMethods.map((method, index) => (
            <button
              key={method.id}
              onClick={() => handleMethodSelect(method)}
              className={`w-full flex items-center justify-between p-4 text-left hover:bg-gray-50 transition-colors ${
                index !== paymentMethods.length - 1 ? 'border-b border-gray-200' : ''
              } ${selectedPaymentMethod?.id === method.id ? 'bg-blue-50' : ''}`}
            >
              <div className="flex items-center space-x-3">
                <div className="flex items-center justify-center w-8 h-8">
                  {method.type === 'card' ? (
                    <CreditCard className="w-5 h-5 text-gray-600" />
                  ) : method.type === 'wallet' ? (
                    <Smartphone className="w-5 h-5 text-gray-600" />
                  ) : (
                    <span className="text-lg">{method.icon}</span>
                  )}
                </div>
                <div>
                  <div className="font-medium text-gray-900">
                    {method.displayName}
                  </div>
                  {method.last4 && (
                    <div className="text-sm text-gray-500">
                      •••• {method.last4}
                    </div>
                  )}
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                {selectedPaymentMethod?.id === method.id && (
                  <Check className="w-5 h-5 text-blue-600" />
                )}
                {method.isAddCard && (
                  <ChevronRight className="w-5 h-5 text-gray-400" />
                )}
              </div>
            </button>
          ))}
        </div>

        {/* Selected Method Info */}
        {selectedPaymentMethod && !selectedPaymentMethod.isAddCard && (
          <div className="mt-4 p-3 bg-blue-50 rounded-lg">
            <div className="text-sm text-blue-800">
              <strong>Selected:</strong> {selectedPaymentMethod.displayName}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default PaymentMethodSelector
