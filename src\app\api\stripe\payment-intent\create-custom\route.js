import { NextRequest, NextResponse } from 'next/server'
import <PERSON><PERSON> from 'stripe'

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY)

export async function POST(request) {
  try {
    const { amount, currency = 'usd', metadata = {} } = await request.json()

    if (!amount) {
      return NextResponse.json(
        { error: 'Amount is required' },
        { status: 400 }
      )
    }

    // Create a PaymentIntent with the order amount and currency
    const paymentIntent = await stripe.paymentIntents.create({
      amount: parseInt(amount),
      currency: currency.toLowerCase(),
      metadata,
      // Enable all available payment methods
      payment_method_types: [
        'card',
        'apple_pay',
        'google_pay',
        'link',
        'paypal',
        'klarna',
        'afterpay_clearpay',
        'affirm',
        'cashapp',
        'us_bank_account'
      ],
      automatic_payment_methods: {
        enabled: true,
        allow_redirects: 'always'
      },
    })

    return NextResponse.json({
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id,
    })
  } catch (error) {
    console.error('Error creating payment intent:', error)
    return NextResponse.json(
      { error: 'Failed to create payment intent' },
      { status: 500 }
    )
  }
}
